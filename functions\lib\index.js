"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.testListings = exports.editListing = exports.createListing = void 0;
// Minimal functions index - only createListing function
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
// Initialize Firebase Admin
admin.initializeApp();
// Helper functions
const verifyAuth = async (context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'The function must be called while authenticated.');
    }
    return context.auth;
};
const handleError = (error) => {
    console.error('Function error:', error);
    if (error instanceof functions.https.HttpsError) {
        throw error;
    }
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
    throw new functions.https.HttpsError('internal', errorMessage, error);
};
console.log('🚀 Minimal Firebase Functions with Listings loading...');
// Create a new listing
exports.createListing = functions.https.onCall(async (data, context) => {
    var _a;
    try {
        console.log('Creating listing with data:', JSON.stringify(data, null, 2));
        const auth = await verifyAuth(context);
        const { title, description, price, category, condition, type, imageURLs } = data;
        // Validate required fields
        if (!title || !description || price === undefined || !category || !condition || !type) {
            throw new functions.https.HttpsError('invalid-argument', 'Missing required fields');
        }
        // Validate listing type
        if (!['sell', 'rent', 'auction'].includes(type)) {
            throw new functions.https.HttpsError('invalid-argument', 'Invalid listing type. Must be one of: sell, rent, auction');
        }
        // Validate condition
        if (!['new', 'like_new', 'very_good', 'good', 'fair', 'poor'].includes(condition)) {
            throw new functions.https.HttpsError('invalid-argument', 'Invalid condition. Must be one of: new, like_new, very_good, good, fair, poor');
        }
        // Get user data to include university
        const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();
        if (!userDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'User not found');
        }
        const userData = userDoc.data();
        console.log('User data:', JSON.stringify(userData, null, 2));
        // Get university from user data or extract from email
        let university = userData === null || userData === void 0 ? void 0 : userData.university;
        console.log('Initial university:', university);
        if (!university && (userData === null || userData === void 0 ? void 0 : userData.email)) {
            console.log('Extracting university from email:', userData.email);
            // Extract university from email domain as fallback
            const emailParts = userData.email.split('@');
            const domain = emailParts[1];
            university = domain.split('.')[0];
            // Capitalize university name
            university = university.charAt(0).toUpperCase() + university.slice(1);
            console.log('Extracted university:', university);
            // Update user profile with university
            await admin.firestore().collection('users').doc(auth.uid).update({
                university,
                updatedAt: admin.firestore.Timestamp.now()
            });
        }
        else if (!university && ((_a = auth.token) === null || _a === void 0 ? void 0 : _a.email)) {
            console.log('Extracting university from auth token email:', auth.token.email);
            // Try to get email from auth token as fallback
            const emailParts = auth.token.email.split('@');
            const domain = emailParts[1];
            university = domain.split('.')[0];
            // Capitalize university name
            university = university.charAt(0).toUpperCase() + university.slice(1);
            console.log('Extracted university from token:', university);
            // Update user profile with university and email
            await admin.firestore().collection('users').doc(auth.uid).update({
                university,
                email: auth.token.email,
                updatedAt: admin.firestore.Timestamp.now()
            });
        }
        if (!university) {
            console.error('Unable to determine university. User data:', userData, 'Auth token:', auth.token);
            throw new functions.https.HttpsError('failed-precondition', 'Unable to determine university from user profile or email. Please update your profile.');
        }
        console.log('Final university:', university);
        // Create the listing object
        const listing = {
            title,
            description,
            price: Number(price),
            category,
            condition: condition,
            type: type,
            ownerId: auth.uid,
            ownerName: (userData === null || userData === void 0 ? void 0 : userData.name) || 'Anonymous',
            university: university,
            imageURLs: imageURLs || [],
            status: 'active',
            visibility: data.visibility || 'university',
            createdAt: admin.firestore.Timestamp.now(),
            // Add delivery method fields
            deliveryMethod: data.deliveryMethod || 'in_person'
        };
        // Only add shippingOptions if it exists and has valid data
        if (data.shippingOptions && Object.keys(data.shippingOptions).length > 0) {
            listing.shippingOptions = data.shippingOptions;
        }
        // Add type-specific fields
        if (type === 'rent') {
            if (data.rentalPeriod)
                listing.rentalPeriod = data.rentalPeriod;
            if (data.weeklyPrice)
                listing.weeklyPrice = Number(data.weeklyPrice);
            if (data.monthlyPrice)
                listing.monthlyPrice = Number(data.monthlyPrice);
            if (data.startDate)
                listing.startDate = data.startDate;
            if (data.endDate)
                listing.endDate = data.endDate;
        }
        if (type === 'auction') {
            if (data.startingBid)
                listing.startingBid = Number(data.startingBid);
            if (data.auctionStartDate)
                listing.auctionStartDate = data.auctionStartDate;
            if (data.auctionStartTime)
                listing.auctionStartTime = data.auctionStartTime;
            if (data.auctionEndDate)
                listing.auctionEndDate = data.auctionEndDate;
            if (data.auctionEndTime)
                listing.auctionEndTime = data.auctionEndTime;
            if (data.auctionDuration)
                listing.auctionDuration = data.auctionDuration;
        }
        // Add to Firestore
        console.log('Adding listing to Firestore:', JSON.stringify(listing, null, 2));
        const docRef = await admin.firestore().collection('listings').add(listing);
        console.log('Listing created successfully with ID:', docRef.id);
        return {
            success: true,
            data: Object.assign({ id: docRef.id }, listing)
        };
    }
    catch (error) {
        return handleError(error);
    }
});
// Edit an existing listing
exports.editListing = functions.https.onCall(async (data, context) => {
    try {
        console.log('Editing listing with data:', JSON.stringify(data, null, 2));
        const auth = await verifyAuth(context);
        const { listingId, title, description, price, category, condition, type, imageURLs, status } = data;
        if (!listingId) {
            throw new functions.https.HttpsError('invalid-argument', 'Listing ID is required');
        }
        // Get the listing
        const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
        if (!listingDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Listing not found');
        }
        const listing = listingDoc.data();
        // Check if the user is the owner
        if ((listing === null || listing === void 0 ? void 0 : listing.ownerId) !== auth.uid) {
            throw new functions.https.HttpsError('permission-denied', 'Only the owner can edit this listing');
        }
        // Create update object
        const updateData = {
            updatedAt: admin.firestore.Timestamp.now()
        };
        if (title !== undefined)
            updateData.title = title;
        if (description !== undefined)
            updateData.description = description;
        if (price !== undefined)
            updateData.price = Number(price);
        if (category !== undefined)
            updateData.category = category;
        if (condition !== undefined)
            updateData.condition = condition;
        if (type !== undefined) {
            if (!['sell', 'rent', 'auction'].includes(type)) {
                throw new functions.https.HttpsError('invalid-argument', 'Invalid listing type. Must be one of: sell, rent, auction');
            }
            updateData.type = type;
        }
        if (imageURLs !== undefined)
            updateData.imageURLs = imageURLs;
        if (status !== undefined) {
            if (!['active', 'sold', 'pending', 'deleted'].includes(status)) {
                throw new functions.https.HttpsError('invalid-argument', 'Invalid status. Must be one of: active, sold, pending, deleted');
            }
            updateData.status = status;
        }
        // Update the listing
        await admin.firestore().collection('listings').doc(listingId).update(updateData);
        console.log('Listing updated successfully:', listingId);
        return {
            success: true,
            data: Object.assign(Object.assign({ id: listingId }, listing), updateData)
        };
    }
    catch (error) {
        console.error('Error editing listing:', error);
        return handleError(error);
    }
});
// Test function to verify deployment
exports.testListings = functions
    .https.onRequest(async (_req, res) => {
    res.json({
        success: true,
        message: 'Listings functions working',
        timestamp: new Date().toISOString()
    });
});
